# YouTube Subtitle Downloader

A simple command-line tool to download subtitles/transcripts from YouTube videos.

## Features

- Download subtitles in different languages (defaults to English)
- List all available languages for a video
- Translate subtitles to other languages
- Save subtitles to a file
- Fallback to English or any available language if requested language is not available

## Requirements

- Python 3.6+
- youtube_transcript_api

## Installation

1. Make sure you have Python installed
2. Install the required package:

```bash
pip install youtube_transcript_api
```

## Usage

### Basic Usage

```bash
python main.py <youtube_url_or_video_id>
```

Where `<youtube_url_or_video_id>` can be:

- A full YouTube URL (e.g., `https://www.youtube.com/watch?v=dQw4w9WgXcQ`)
- A short YouTube URL (e.g., `https://youtu.be/dQw4w9WgXcQ`)
- Just the video ID (e.g., `dQw4w9WgXcQ`)

### Options

- `--language` or `-l`: Specify the language code (default: en)
- `--translate` or `-t`: Translate subtitles to the specified language code
- `--list` or `-ls`: List all available languages for the video
- `--output` or `-o`: Save subtitles to a file (default: video_id_language.txt)

### Examples

1. Download English subtitles using a full YouTube URL:

```bash
python main.py "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
```

2. Download English subtitles using a short YouTube URL:

```bash
python main.py "https://youtu.be/dQw4w9WgXcQ"
```

3. Download English subtitles using just the video ID:

```bash
python main.py dQw4w9WgXcQ
```

4. Download Spanish subtitles:

```bash
python main.py "https://www.youtube.com/watch?v=dQw4w9WgXcQ" --language es
```

5. List all available languages:

```bash
python main.py "https://www.youtube.com/watch?v=dQw4w9WgXcQ" --list
```

6. Download English subtitles and translate to French:

```bash
python main.py "https://www.youtube.com/watch?v=dQw4w9WgXcQ" --language en --translate fr
```

7. Save subtitles to a file:

```bash
python main.py "https://www.youtube.com/watch?v=dQw4w9WgXcQ" --output subtitles.txt
```

8. Combine options:

```bash
python main.py "https://www.youtube.com/watch?v=dQw4w9WgXcQ" --language es --translate fr --output french_subtitles.txt
```

## Language Codes

Some common language codes:

- English: `en`
- Spanish: `es`
- French: `fr`
- German: `de`
- Italian: `it`
- Portuguese: `pt`
- Russian: `ru`
- Japanese: `ja`
- Chinese: `zh`

For a complete list of language codes, use the `--list` option to see what's available for a specific video.

## Supported URL Formats

The tool supports various YouTube URL formats:

- Standard YouTube URLs: `https://www.youtube.com/watch?v=VIDEO_ID`
- Short YouTube URLs: `https://youtu.be/VIDEO_ID`
- Mobile YouTube URLs: `https://m.youtube.com/watch?v=VIDEO_ID`
- Embed URLs: `https://www.youtube.com/embed/VIDEO_ID`
- Direct video IDs: `VIDEO_ID`

**Note**: When using URLs with special characters, it's recommended to wrap them in quotes.

## Notes

- If the requested language is not available, the script will try to fall back to English
- If English is not available, it will try to get any available language
- Not all videos have subtitles available
- Translation is only available for certain languages and depends on YouTube's capabilities
